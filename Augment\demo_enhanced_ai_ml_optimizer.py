"""
Demo: Enhanced AI/ML Threshold Optimization System
Works with actual Excel data structure from technical analysis files

🎯 FEATURES DEMONSTRATED:
- Real Excel data loading and transformation
- True signal identification (≥0.5% profit within 15 minutes)
- Multi-algorithm ML optimization
- 14 timeframe combination testing
- Mathematical optimization functions
- Performance validation and reporting
"""

import os
import sys
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_ai_ml_threshold_optimizer import EnhancedAIMLThresholdOptimizer
from datetime import datetime

def demo_automated_data_fetcher():
    """
    🔄 Demo the Automated Technical Data Fetcher
    """
    print("🔄 AUTOMATED TECHNICAL DATA FETCHER DEMO")
    print("=" * 60)
    print("📊 BATCH PROCESSING: Single command for multiple dates and intervals")
    print("⏱️ MULTIPLE INTERVALS: 1, 3, 5, 15, 30, 60 minute intervals")
    print("📁 ORGANIZED OUTPUT: Timestamp folders with batch naming convention")
    print("🚫 SKIP WEEKENDS: Automatically excludes Saturday and Sunday")
    print("🔧 CUSTOMIZABLE: Choose functions, method, and parameters")
    print("📈 READY FOR AI/ML: Generated data ready for threshold optimization")
    print("🚀 NEW: Single login, single command, organized output")
    print("=" * 60)

    try:
        # Initialize optimizer with automated fetcher
        optimizer = EnhancedAIMLThresholdOptimizer()

        # Run automated fetcher
        print("\n🔄 Starting Automated Technical Data Fetcher...")
        output_folder = optimizer.run_automated_technical_fetcher()

        if output_folder:
            print(f"\n✅ Data fetching completed successfully!")
            print(f"📁 Files saved in: {output_folder}")

            # Ask if user wants to run optimization on fetched data
            run_optimization = input("\n🤖 Run AI/ML optimization on fetched data? (y/n): ").strip().lower()
            if run_optimization == 'y':
                # Find the most recent Excel file in the output folder
                excel_files = list(output_folder.glob("*.xlsx"))
                if excel_files:
                    latest_file = max(excel_files, key=lambda x: x.stat().st_mtime)
                    print(f"📊 Using latest file: {latest_file.name}")

                    # Run optimization on the fetched data
                    demo_enhanced_optimization_with_file(str(latest_file))
                else:
                    print("⚠️ No Excel files found in output folder")
        else:
            print("❌ Data fetching was cancelled or failed")

    except Exception as e:
        print(f"❌ Error in automated fetcher demo: {str(e)}")
        import traceback
        traceback.print_exc()

def demo_enhanced_optimization_with_file(excel_file_path: str):
    """
    🎯 Demo the Enhanced AI/ML Threshold Optimization System with specific file
    """
    print(f"\n🚀 ENHANCED AI/ML THRESHOLD OPTIMIZATION")
    print("=" * 60)
    print(f"📊 Using file: {os.path.basename(excel_file_path)}")
    print("=" * 60)

    try:
        # Initialize the enhanced optimizer
        optimizer = EnhancedAIMLThresholdOptimizer()

        # Run comprehensive optimization
        print("🔄 Starting comprehensive threshold optimization...")

        # Prepare data files dictionary
        data_files = {"1min": excel_file_path}

        results = optimizer.comprehensive_threshold_optimization(
            data_files=data_files,
            ticker="DEMO_TICKER"
        )

        if results.get('optimization_successful', False):
            print("✅ Enhanced optimization completed successfully!")
            print(f"📊 Results: {len(results.get('optimized_thresholds', {}))} indicators optimized")
        else:
            print("⚠️ Enhanced optimization completed with issues")
            print("🔧 Check the error messages and adjust as needed")

    except Exception as e:
        print(f"❌ Error in enhanced optimization: {str(e)}")
        import traceback
        traceback.print_exc()

def find_excel_files_smart():
    """
    🔍 Smart Excel file detection with multiple options:
    1. Find latest batch folder
    2. Choose from top 5 recent batch folders
    3. Enter custom folder name
    Returns dictionary of timeframe -> file_path mappings
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # Option 1: Find all batch folders
    batch_folders = []
    for item in os.listdir(current_dir):
        if os.path.isdir(os.path.join(current_dir, item)) and item.startswith('batch_analysis_'):
            folder_path = os.path.join(current_dir, item)
            # Get folder creation time
            folder_time = os.path.getctime(folder_path)
            batch_folders.append((item, folder_path, folder_time))

    # Sort by creation time (newest first)
    batch_folders.sort(key=lambda x: x[2], reverse=True)

    if not batch_folders:
        print("❌ No batch folders found")
        print("💡 Please run the automated data fetcher first")
        return {}

    print(f"📁 Found {len(batch_folders)} batch folders")
    print("\n🔍 FILE SELECTION OPTIONS:")
    print("1. 📊 Use latest batch folder")
    print("2. 📋 Choose from top 5 recent folders")
    print("3. ✏️ Enter custom folder name")

    choice = input("\nEnter your choice (1/2/3): ").strip()

    selected_folder = None

    if choice == '1':
        # Use latest folder
        selected_folder = batch_folders[0][1]
        print(f"📁 Using latest folder: {batch_folders[0][0]}")

    elif choice == '2':
        # Show top 5 and let user choose
        print("\n📋 TOP 5 RECENT BATCH FOLDERS:")
        top_5 = batch_folders[:5]
        for i, (name, path, time) in enumerate(top_5, 1):
            time_str = datetime.fromtimestamp(time).strftime('%Y-%m-%d %H:%M:%S')
            print(f"  {i}. {name} (Created: {time_str})")

        try:
            folder_choice = int(input(f"\nSelect folder (1-{len(top_5)}): ").strip())
            if 1 <= folder_choice <= len(top_5):
                selected_folder = top_5[folder_choice - 1][1]
                print(f"📁 Selected: {top_5[folder_choice - 1][0]}")
            else:
                print("❌ Invalid selection")
                return {}
        except ValueError:
            print("❌ Invalid input")
            return {}

    elif choice == '3':
        # Enter custom folder name
        folder_name = input("📁 Enter folder name: ").strip()
        folder_path = os.path.join(current_dir, folder_name)
        if os.path.exists(folder_path) and os.path.isdir(folder_path):
            selected_folder = folder_path
            print(f"📁 Using custom folder: {folder_name}")
        else:
            print(f"❌ Folder not found: {folder_name}")
            return {}
    else:
        print("❌ Invalid choice")
        return {}

    # Find Excel files in selected folder and organize by timeframe
    excel_files = []
    for file in os.listdir(selected_folder):
        if file.endswith('.xlsx') and not file.startswith('~') and not file.startswith('BATCH_'):
            file_path = os.path.join(selected_folder, file)
            file_time = os.path.getctime(file_path)
            excel_files.append((file, file_path, file_time))

    if not excel_files:
        print(f"❌ No Excel files found in folder: {os.path.basename(selected_folder)}")
        return {}

    # Sort by creation time (newest first)
    excel_files.sort(key=lambda x: x[2], reverse=True)

    print(f"\n📄 Found {len(excel_files)} Excel files:")

    # Organize files by timeframe
    timeframe_files = {}
    for file, file_path, file_time in excel_files:
        # Extract timeframe from filename (e.g., "1min", "3min", etc.)
        if '_1min_' in file:
            timeframe_files['1min'] = file_path
        elif '_3min_' in file:
            timeframe_files['3min'] = file_path
        elif '_5min_' in file:
            timeframe_files['5min'] = file_path
        elif '_15min_' in file:
            timeframe_files['15min'] = file_path
        elif '_30min_' in file:
            timeframe_files['30min'] = file_path
        elif '_60min_' in file:
            timeframe_files['60min'] = file_path

    # Show organized files
    for timeframe in ['1min', '3min', '5min', '15min', '30min', '60min']:
        if timeframe in timeframe_files:
            filename = os.path.basename(timeframe_files[timeframe])
            print(f"  ✅ {timeframe:>6}: {filename}")
        else:
            print(f"  ❌ {timeframe:>6}: Not found")

    if not timeframe_files:
        print("❌ No timeframe-specific files found")
        print("💡 Files should contain timeframe indicators like '_1min_', '_3min_', etc.")
        return {}

    return timeframe_files

def demo_enhanced_optimization():
    """Demonstrate the enhanced AI/ML threshold optimization system with smart file detection"""

    print("🚀 DEMO: REVOLUTIONARY Enhanced AI/ML Threshold Optimization System")
    print("================================================================================")
    print("🎯 OBJECTIVE: Learn from actual Excel data for maximum accuracy")
    print("📊 TRUE SIGNAL: 1min signal → ≥1.0% profit within 15 minutes")
    print("🤖 ML ALGORITHMS: Multi-algorithm ensemble optimization")
    print("🔍 14 TIMEFRAME COMBINATIONS: Complete confirmation learning")
    print("📈 MATHEMATICAL: Advanced optimization functions")
    print("🔧 EXCEL INTEGRATION: Works with actual technical analysis files")
    print("🔄 ITERATIVE TRAINING: Up to 15 iterations until 98% convergence")
    print("📊 COMPREHENSIVE REPORTING: Multi-sheet Excel analysis")
    print("💼 PROFESSIONAL APPROACH: Enhanced manual signal detection by profit analysis")
    print("🎯 EXTENDED PROFIT WINDOW SCANNING: Look 30min BEFORE + 15min DURING profit")
    print("🔍 INTELLIGENT SIGNAL EXTRACTION: Find realistic values near professional thresholds")
    print("📊 MULTIPLE PROFIT THRESHOLDS: 0.3%, 0.4%, 0.5%, 0.6%, 0.7% for comprehensive analysis")
    print("⚡ ENHANCED PGO_14 DETECTION: Specialized logic for most accurate indicator")
    print("🔧 PROFESSIONAL VALUE FILTERING: Target values near industry standards")
    print("📈 SOLVE 70% DEVIATION ISSUE: Find actual signal values that caused profits")
    print("🔍 SMART FILE DETECTION: Auto-detect batch folders and Excel files")
    print("")
    print("🎯 REVOLUTIONARY ENHANCEMENTS:")
    print("🚫 ULTRA-STRICT FALSE SIGNAL FILTERING: Eliminate consecutive signals")
    print("⚡ TIGHTENED THRESHOLDS: 50% more restrictive for stronger signals only")
    print("🔍 ENHANCED OUTLIER DETECTION: 2% tolerance instead of 5%")
    print("🚫 AGGRESSIVE OUTLIER EXCLUSION: 1.5x multiplier instead of 2x")
    print("⏰ EXTENDED SIGNAL GAPS: 8 minutes minimum between signals")
    print("🔄 OPPOSITE CROSSOVER REQUIREMENT: Must cross opposite threshold first")
    print("🛡️ ACTIVE PERIOD BLOCKING: 10+ minute buffers around true signals")
    print("📊 PROPER SIGNAL CATEGORIZATION: True/False/Outlier separation")
    print("================================================================================")

    # Initialize the enhanced optimizer
    optimizer = EnhancedAIMLThresholdOptimizer()

    # Smart file detection with multiple options
    existing_files = find_excel_files_smart()

    if not existing_files:
        print("❌ No suitable Excel files found")
        print("💡 Please run the automated data fetcher first")
        return

    print("\n🔍 FOUND DATA FILES:")
    print("-" * 50)

    for timeframe, filepath in existing_files.items():
        print(f"✅ {timeframe:>6}: {os.path.basename(filepath)}")

    if not existing_files:
        print("\n❌ No data files found. Please check file paths.")
        return None
    
    print(f"\n📊 Found {len(existing_files)} timeframe files")
    
    # Test Excel data loading
    print("\n🔧 TESTING EXCEL DATA LOADING:")
    print("-" * 50)
    
    sample_file = list(existing_files.values())[0]
    sample_timeframe = list(existing_files.keys())[0]
    
    print(f"📂 Loading sample file: {os.path.basename(sample_file)}")
    sample_data = optimizer.load_excel_data(sample_file, sample_timeframe)
    
    if sample_data is not None:
        print(f"✅ Successfully loaded and transformed data")
        print(f"   📊 Shape: {sample_data.shape}")
        print(f"   🕒 Time range: {sample_data.index[0]} to {sample_data.index[-1]}")
        print(f"   📈 Columns: {len(sample_data.columns)} indicators")
        
        # Show available indicators
        target_indicators_found = []
        for target in optimizer.target_indicators:
            matching_cols = [col for col in sample_data.columns if target in col]
            if matching_cols:
                target_indicators_found.extend(matching_cols)
        
        if target_indicators_found:
            print(f"   🎯 Target indicators found: {len(target_indicators_found)}")
            for indicator in target_indicators_found[:3]:  # Show first 3
                print(f"      - {indicator}")
            if len(target_indicators_found) > 3:
                print(f"      ... and {len(target_indicators_found) - 3} more")
        else:
            print("   ⚠️ No target indicators found - will use synthetic data")
    else:
        print("❌ Failed to load sample data")
        return None
    
    # Run the comprehensive optimization
    try:
        print("\n🚀 STARTING COMPREHENSIVE OPTIMIZATION...")
        print("=" * 80)
        
        results = optimizer.comprehensive_threshold_optimization(
            existing_files, 
            ticker="NATURALGAS26AUG25_MCX"
        )
        
        # Display results
        display_enhanced_results(results)
        
        return results
        
    except Exception as e:
        print(f"❌ Error during optimization: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def display_enhanced_results(results):
    """Display comprehensive optimization results"""
    
    print("\n🎉 ENHANCED OPTIMIZATION RESULTS SUMMARY")
    print("=" * 80)
    
    if not results or not results.get('validation_successful'):
        print("❌ Optimization was not successful")
        return
    
    # Display iterative training results
    iterative_results = results.get('iterative_results', {})
    if iterative_results:
        print("\n🔄 ITERATIVE TRAINING RESULTS:")
        print("-" * 40)
        print(f"🔄 Iterations Completed:     {iterative_results.get('iterations_completed', 0)}")
        print(f"🎯 Convergence Achieved:     {'YES' if iterative_results.get('convergence_achieved', False) else 'NO'}")

        # Show iteration progress
        all_iterations = iterative_results.get('all_iterations_data', [])
        if all_iterations:
            print(f"📈 Training Progress:")
            for i, iteration_data in enumerate(all_iterations[-3:], max(1, len(all_iterations)-2)):  # Show last 3 iterations
                results_data = iteration_data['results']
                total_true = sum(len(r['true_signals']) for r in results_data.values())
                total_false = sum(len(r['false_signals']) for r in results_data.values())
                total_signals = total_true + total_false
                rate = total_true / max(total_signals, 1) * 100
                print(f"   Iteration {iteration_data['iteration']}: {total_true} true signals ({rate:.1f}%)")

    # Display performance metrics
    metrics = results.get('final_metrics', {})
    print("\n📊 PERFORMANCE METRICS:")
    print("-" * 40)
    print(f"🎯 True Signal Capture Rate: {metrics.get('true_signal_capture_rate', 0):.1f}%")
    print(f"❌ False Signal Rate:        {metrics.get('false_signal_rate', 0):.1f}%")
    print(f"💰 Average Profit/Signal:    {metrics.get('average_profit', 0):.2f}%")
    print(f"📈 Sharpe Ratio:             {metrics.get('sharpe_ratio', 0):.2f}")
    print(f"🏆 Best Combination Score:   {metrics.get('best_combination_score', 0):.3f}")
    
    # Display success criteria
    criteria = results.get('success_criteria', {})
    print("\n🎯 SUCCESS CRITERIA:")
    print("-" * 40)
    print(f"✅ True Signal Capture ≥95%: {'PASS' if criteria.get('true_signal_capture_rate_95') else 'FAIL'}")
    print(f"✅ False Signal Rate ≤30%:   {'PASS' if criteria.get('false_signal_rate_30') else 'FAIL'}")
    print(f"✅ Average Profit ≥0.5%:     {'PASS' if criteria.get('average_profit_05') else 'FAIL'}")
    print(f"✅ Sharpe Ratio ≥2.0:        {'PASS' if criteria.get('sharpe_ratio_2') else 'FAIL'}")
    print(f"🏆 ALL CRITERIA MET:         {'YES' if criteria.get('all_criteria_met') else 'NO'}")
    
    # Display best timeframe combinations
    best_combinations = results.get('best_combinations', [])
    if best_combinations:
        print("\n🏆 BEST TIMEFRAME COMBINATIONS:")
        print("-" * 40)
        for i, combo in enumerate(best_combinations[:5], 1):  # Show top 5
            timeframes = ' + '.join(combo['timeframes'])
            score = combo['score']
            print(f"{i}. {timeframes:<25} (Score: {score:.3f})")
    
    # Display optimized thresholds sample
    optimized_thresholds = results.get('optimized_thresholds', {})
    if optimized_thresholds:
        print("\n🔧 OPTIMIZED THRESHOLDS (Sample):")
        print("-" * 40)
        for indicator in list(optimized_thresholds.keys())[:3]:  # Show first 3 indicators
            print(f"\n{indicator}:")
            thresholds = optimized_thresholds[indicator]
            for key, value in thresholds.items():
                if isinstance(value, (int, float)):
                    print(f"  {key}: {value:.2f}")
                else:
                    print(f"  {key}: {value}")
    
    # Display Excel report information
    excel_report = results.get('excel_report', '')
    if excel_report:
        print("\n📊 COMPREHENSIVE EXCEL REPORT:")
        print("-" * 40)
        print(f"📄 Excel File: {excel_report}")
        print("📋 Sheets Created:")
        print("   1. Summary - Overall optimization results")
        print("   2. True_Signals - All profitable signals found")
        print("   3. False_Signals - Non-profitable signals")
        print("   4. Iteration_History - Training progress")
        print("   5. Threshold_Evolution - How thresholds changed")
        print("   6. Model_Performance - ML model comparison")
        print("   7. Timeframe_Analysis - Best combinations")
        print("   8. Signal_Timeline - Chronological signal view")
        print("   9. Outlier_Analysis - Special cases detected")
        print("   10. Profitability_Analysis - Profit statistics")

    print("\n" + "=" * 80)
    print("🎉 ENHANCED OPTIMIZATION COMPLETE!")
    print("📄 Detailed JSON report saved")
    print("📊 Comprehensive Excel analysis generated")
    print("🔧 Optimized thresholds ready for trading implementation")
    print("🔄 Iterative training ensures maximum signal capture")

def test_excel_data_structure():
    """Test and display Excel data structure"""
    
    print("\n🔬 TESTING EXCEL DATA STRUCTURE")
    print("=" * 50)
    
    filename = 'technical_analysis_NATURALGAS26AUG25_MCX_signals_1min_20250714_020337.xlsx'
    
    if not os.path.exists(filename):
        print(f"❌ Test file not found: {filename}")
        return
    
    optimizer = EnhancedAIMLThresholdOptimizer()
    
    print(f"📂 Loading: {filename}")
    data = optimizer.load_excel_data(filename, '1min')
    
    if data is not None:
        print(f"✅ Data loaded successfully")
        print(f"   📊 Shape: {data.shape}")
        print(f"   🕒 Time columns: {len(data.index)}")
        print(f"   📈 Indicator columns: {len(data.columns)}")
        
        # Show sample data
        print(f"\n📋 Sample Data (first 5 rows, first 5 columns):")
        print(data.iloc[:5, :5])
        
        # Show available indicators
        print(f"\n🎯 Available Indicators (first 10):")
        for i, col in enumerate(data.columns[:10]):
            print(f"   {i+1:2d}. {col}")
        if len(data.columns) > 10:
            print(f"   ... and {len(data.columns) - 10} more")
    else:
        print("❌ Failed to load data")

def main():
    """Main demo execution with options"""

    print(f"🕒 Enhanced Demo started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    print("\n🔄 ENHANCED AI/ML THRESHOLD OPTIMIZATION SYSTEM")
    print("=" * 60)
    print("Choose demo mode:")
    print("1. 🔄 Automated Technical Data Fetcher (Fetch fresh data)")
    print("2. 🎯 Enhanced Optimization (Use existing Excel file)")
    print("3. 📊 Test Excel Data Structure")
    print("=" * 60)

    choice = input("Enter your choice (1/2/3): ").strip()

    if choice == "1":
        # Run automated data fetcher
        demo_automated_data_fetcher()
    elif choice == "2":
        # Test Excel data structure first
        test_excel_data_structure()

        # Run the comprehensive demo
        results = demo_enhanced_optimization()

        if results:
            print("\n✅ Enhanced demo completed successfully!")
            print("📊 Review the results above for optimization insights")
            print("🔧 The system successfully processed actual Excel data")
        else:
            print("\n⚠️ Enhanced demo completed with issues")
            print("🔧 Check the error messages and adjust as needed")
    elif choice == "3":
        # Just test Excel data structure
        test_excel_data_structure()
    else:
        print("❌ Invalid choice. Please run again and select 1, 2, or 3.")

    print(f"\n🕒 Enhanced demo finished: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
