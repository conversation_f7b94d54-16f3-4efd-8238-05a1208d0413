{"timestamp": "20250720_025047", "manual_signals_count": 19, "individual_signal_results": {"PGO_14": {"indicator": "PGO_14", "training_successful": true, "valid_signals_count": 5, "timeframe_combinations": {"15min": {"training_successful": true, "combination": ["15min"], "combination_name": "15min", "available_timeframes": ["15min"], "total_signals": 5, "true_positives": 0, "false_positives": 0, "false_negatives": 5, "accuracy": 0.0, "precision": 0, "recall": 0.0}, "3min+15min": {"training_successful": true, "combination": ["3min", "15min"], "combination_name": "3min+15min", "available_timeframes": ["3min", "15min"], "total_signals": 5, "true_positives": 0, "false_positives": 0, "false_negatives": 5, "accuracy": 0.0, "precision": 0, "recall": 0.0}, "5min+15min": {"training_successful": true, "combination": ["5min", "15min"], "combination_name": "5min+15min", "available_timeframes": ["5min", "15min"], "total_signals": 5, "true_positives": 0, "false_positives": 0, "false_negatives": 5, "accuracy": 0.0, "precision": 0, "recall": 0.0}, "3min": {"training_successful": true, "combination": ["3min"], "combination_name": "3min", "available_timeframes": ["3min"], "total_signals": 5, "true_positives": 0, "false_positives": 0, "false_negatives": 5, "accuracy": 0.0, "precision": 0, "recall": 0.0}, "5min": {"training_successful": true, "combination": ["5min"], "combination_name": "5min", "available_timeframes": ["5min"], "total_signals": 5, "true_positives": 1, "false_positives": 0, "false_negatives": 4, "accuracy": 20.0, "precision": 100.0, "recall": 20.0}, "3min+15min+30min": {"training_successful": true, "combination": ["3min", "15min", "30min"], "combination_name": "3min+15min+30min", "available_timeframes": ["3min", "15min", "30min"], "total_signals": 5, "true_positives": 0, "false_positives": 0, "false_negatives": 5, "accuracy": 0.0, "precision": 0, "recall": 0.0}, "5min+15min+30min": {"training_successful": true, "combination": ["5min", "15min", "30min"], "combination_name": "5min+15min+30min", "available_timeframes": ["5min", "15min", "30min"], "total_signals": 5, "true_positives": 0, "false_positives": 0, "false_negatives": 5, "accuracy": 0.0, "precision": 0, "recall": 0.0}, "15min+30min": {"training_successful": true, "combination": ["15min", "30min"], "combination_name": "15min+30min", "available_timeframes": ["15min", "30min"], "total_signals": 5, "true_positives": 0, "false_positives": 0, "false_negatives": 5, "accuracy": 0.0, "precision": 0, "recall": 0.0}, "3min+30min": {"training_successful": true, "combination": ["3min", "30min"], "combination_name": "3min+30min", "available_timeframes": ["3min", "30min"], "total_signals": 5, "true_positives": 0, "false_positives": 0, "false_negatives": 5, "accuracy": 0.0, "precision": 0, "recall": 0.0}, "5min+30min": {"training_successful": true, "combination": ["5min", "30min"], "combination_name": "5min+30min", "available_timeframes": ["5min", "30min"], "total_signals": 5, "true_positives": 0, "false_positives": 0, "false_negatives": 5, "accuracy": 0.0, "precision": 0, "recall": 0.0}, "5min+15min+30min+60min": {"training_successful": true, "combination": ["5min", "15min", "30min", "60min"], "combination_name": "5min+15min+30min+60min", "available_timeframes": ["5min", "15min", "30min", "60min"], "total_signals": 5, "true_positives": 0, "false_positives": 0, "false_negatives": 5, "accuracy": 0.0, "precision": 0, "recall": 0.0}, "5min+60min": {"training_successful": true, "combination": ["5min", "60min"], "combination_name": "5min+60min", "available_timeframes": ["5min", "60min"], "total_signals": 5, "true_positives": 0, "false_positives": 0, "false_negatives": 5, "accuracy": 0.0, "precision": 0, "recall": 0.0}, "15min+60min": {"training_successful": true, "combination": ["15min", "60min"], "combination_name": "15min+60min", "available_timeframes": ["15min", "60min"], "total_signals": 5, "true_positives": 0, "false_positives": 0, "false_negatives": 5, "accuracy": 0.0, "precision": 0, "recall": 0.0}, "3min+15min+60min": {"training_successful": true, "combination": ["3min", "15min", "60min"], "combination_name": "3min+15min+60min", "available_timeframes": ["3min", "15min", "60min"], "total_signals": 5, "true_positives": 0, "false_positives": 0, "false_negatives": 5, "accuracy": 0.0, "precision": 0, "recall": 0.0}}, "best_combination": "5min", "individual_interval_results": {"1min": {"training_successful": true, "timeframe": "1min", "indicator_column": "PGO_14_technical", "features_count": 5, "models_results": {"RandomForest": {"score": 1.0, "std": 0.0}, "GradientBoosting": {"error": "\nAll the 3 fits failed.\nIt is very likely that your model is misconfigured.\nYou can try to debug the error by setting error_score='raise'.\n\nBelow are more details about the failures:\n--------------------------------------------------------------------------------\n3 <USER> <GROUP> with the following error:\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\Shoonya1\\lib\\site-packages\\sklearn\\model_selection\\_validation.py\", line 866, in _fit_and_score\n    estimator.fit(X_train, y_train, **fit_params)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\Shoonya1\\lib\\site-packages\\sklearn\\base.py\", line 1389, in wrapper\n    return fit_method(estimator, *args, **kwargs)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\Shoonya1\\lib\\site-packages\\sklearn\\ensemble\\_gb.py\", line 669, in fit\n    y = self._encode_y(y=y, sample_weight=None)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\Shoonya1\\lib\\site-packages\\sklearn\\ensemble\\_gb.py\", line 1527, in _encode_y\n    raise ValueError(\nValueError: y contains 1 class after sample_weight trimmed classes with zero weights, while a minimum of 2 classes are required.\n"}, "SVM": {"error": "\nAll the 3 fits failed.\nIt is very likely that your model is misconfigured.\nYou can try to debug the error by setting error_score='raise'.\n\nBelow are more details about the failures:\n--------------------------------------------------------------------------------\n3 <USER> <GROUP> with the following error:\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\Shoonya1\\lib\\site-packages\\sklearn\\model_selection\\_validation.py\", line 866, in _fit_and_score\n    estimator.fit(X_train, y_train, **fit_params)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\Shoonya1\\lib\\site-packages\\sklearn\\base.py\", line 1389, in wrapper\n    return fit_method(estimator, *args, **kwargs)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\Shoonya1\\lib\\site-packages\\sklearn\\svm\\_base.py\", line 206, in fit\n    y = self._validate_targets(y)\n  File \"C:\\Users\\<USER>\\anaconda3\\envs\\Shoonya1\\lib\\site-packages\\sklearn\\svm\\_base.py\", line 750, in _validate_targets\n    raise ValueError(\nValueError: The number of classes has to be greater than one; got 1 class\n"}, "NeuralNetwork": {"score": 1.0, "std": 0.0}}, "best_model": "RandomForest", "best_score": 1.0}, "3min": {"training_successful": false, "reason": "insufficient_features"}, "5min": {"training_successful": false, "reason": "insufficient_features"}, "15min": {"training_successful": false, "reason": "insufficient_features"}, "30min": {"training_successful": false, "reason": "indicator_not_found"}, "60min": {"training_successful": false, "reason": "indicator_not_found"}}, "final_optimized_thresholds": {"indicator": "PGO_14", "initial_oversold": -3.2, "initial_overbought": 3.2, "optimized_thresholds": {"detection_oversold": -3.573646179910316, "confirmation_oversold": -2.858916943928253, "detection_overbought": 3.0458976904349733, "confirmation_overbought": 2.4367181523479786}, "buy_signals_count": 2, "sell_signals_count": 3, "optimization_successful": true}, "best_accuracy": 20.0}, "CCI_14_0.015": {"training_successful": false, "reason": "insufficient_data"}, "SMI_5_20_5": {"training_successful": false, "reason": "insufficient_data"}, "SMIo_5_20_5": {"training_successful": false, "reason": "insufficient_data"}, "BIAS_SMA_26": {"training_successful": false, "reason": "insufficient_data"}, "CG_10": {"training_successful": false, "reason": "insufficient_data"}, "ACCBU_20": {"training_successful": false, "reason": "insufficient_data"}, "QQE_14_5_4.236_RSIMA": {"training_successful": false, "reason": "insufficient_data"}}, "summary": {"total_indicators_processed": 8, "successful_trainings": 1, "best_accuracies": {"PGO_14": 20.0, "CCI_14_0.015": 0, "SMI_5_20_5": 0, "SMIo_5_20_5": 0, "BIAS_SMA_26": 0, "CG_10": 0, "ACCBU_20": 0, "QQE_14_5_4.236_RSIMA": 0}}}