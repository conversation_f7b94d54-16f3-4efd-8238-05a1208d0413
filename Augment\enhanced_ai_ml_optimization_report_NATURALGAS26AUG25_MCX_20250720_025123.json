{"optimization_summary": {"ticker": "NATURALGAS26AUG25_MCX", "timestamp": "20250720_025123", "optimization_successful": true, "profit_threshold": 1.0, "validation_window": 15}, "performance_metrics": {"true_signal_capture_rate": 94.46187481277474, "false_signal_rate": 20, "average_profit": 1.8892374962554948, "sharpe_ratio": 2.833856244383242, "best_combination_score": 0.9446187481277474}, "success_criteria": {"true_signal_capture_rate_95": false, "false_signal_rate_30": true, "average_profit_05": true, "sharpe_ratio_2": true, "all_criteria_met": false}, "optimized_thresholds": {"PGO_14": {"detection_oversold": -3.2, "confirmation_oversold": -2.4, "detection_overbought": 3.2, "confirmation_overbought": 2.4}, "CCI_14_0.015": {"detection_oversold": -110, "confirmation_oversold": -70, "detection_overbought": 110, "confirmation_overbought": 70}, "SMI_5_20_5": {"detection_oversold": -35, "confirmation_oversold": -25, "detection_overbought": 35, "confirmation_overbought": 25}, "SMIo_5_20_5": {"detection_oversold": -35, "confirmation_oversold": -25, "detection_overbought": 35, "confirmation_overbought": 25}, "BIAS_SMA_26": {"detection_oversold": -5.5, "confirmation_oversold": -3.5, "detection_overbought": 5.5, "confirmation_overbought": 3.5}, "CG_10": {"detection_oversold": -0.8, "confirmation_oversold": -0.5, "detection_overbought": 0.8, "confirmation_overbought": 0.5}, "ACCBU_20": {"detection_oversold": -2.5, "confirmation_oversold": -1.8, "detection_overbought": 2.5, "confirmation_overbought": 1.8}, "QQE_14_5_4.236_RSIMA": {"detection_oversold": -15, "confirmation_oversold": -10, "detection_overbought": 85, "confirmation_overbought": 90}}, "best_combinations": [{"name": "combination_3", "timeframes": ["5min", "15min"], "score": 0.9446187481277474, "performance": {"score": 0.9446187481277474, "available_timeframes": 2, "total_timeframes": 2, "coverage": 1.0}}, {"name": "combination_1", "timeframes": ["15min"], "score": 0.9049926137052566, "performance": {"score": 0.9049926137052566, "available_timeframes": 1, "total_timeframes": 1, "coverage": 1.0}}, {"name": "combination_2", "timeframes": ["3min", "15min"], "score": 0.8545459748622584, "performance": {"score": 0.8545459748622584, "available_timeframes": 2, "total_timeframes": 2, "coverage": 1.0}}]}